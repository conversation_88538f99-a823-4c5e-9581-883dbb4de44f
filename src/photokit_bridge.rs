//! PhotoKit 框架桥接层
//!
//! 此模块提供与 macOS PhotoKit 框架的 Objective-C 互操作功能。
//! 实现了权限状态检查和权限请求的底层桥接代码。

use crate::{PhotoKitAccessLevel, PhotoKitAuthorizationStatus};
use objc2::{class, msg_send, runtime::Bool};

/// PhotoKit 权限桥接错误类型
#[derive(Debug, thiserror::Error)]
pub enum PhotoKitBridgeError {
    #[error("PhotoKit 框架不可用")]
    FrameworkUnavailable,
    #[error("权限请求超时")]
    RequestTimeout,
    #[error("权限请求失败: {0}")]
    RequestFailed(String),
    #[error("无效的权限级别: {0}")]
    InvalidAccessLevel(i32),
    #[error("无效的授权状态: {0}")]
    InvalidAuthorizationStatus(i32),
}

/// PhotoKit 权限桥接器
///
/// 提供与 PhotoKit 框架交互的底层功能，包括权限状态检查和权限请求。
pub struct PhotoKitBridge;

impl PhotoKitBridge {
    /// 创建新的 PhotoKit 桥接器实例
    pub fn new() -> Self {
        Self
    }

    /// 检查指定权限级别的当前授权状态
    ///
    /// # Arguments
    /// * `access_level` - 要检查的权限级别
    ///
    /// # Returns
    /// 返回当前的权限授权状态
    ///
    /// # Errors
    /// 如果 PhotoKit 框架不可用或权限级别无效，返回错误
    pub fn check_authorization_status(
        &self,
        access_level: PhotoKitAccessLevel,
    ) -> Result<PhotoKitAuthorizationStatus, PhotoKitBridgeError> {
        unsafe {
            // 检查 PHPhotoLibrary 类是否可用
            // 在 objc2 中，如果类不存在，class! 宏会返回一个有效的引用
            // 我们通过尝试调用方法来检查框架是否真正可用
            let ph_photo_library_class = class!(PHPhotoLibrary);

            // 调用 PHPhotoLibrary.authorizationStatusForAccessLevel:
            let access_level_value = access_level.to_native_value();
            let status: i32 = msg_send![
                ph_photo_library_class,
                authorizationStatusForAccessLevel: access_level_value
            ];

            // 将原生状态值转换为我们的枚举
            PhotoKitAuthorizationStatus::from_native_value(status)
                .ok_or(PhotoKitBridgeError::InvalidAuthorizationStatus(status))
        }
    }

    /// 请求指定权限级别的授权
    ///
    /// 注意：此方法会触发系统权限对话框，但由于 Objective-C 回调的复杂性，
    /// 当前实现返回请求后的状态检查结果。
    ///
    /// # Arguments
    /// * `access_level` - 要请求的权限级别
    ///
    /// # Returns
    /// 返回权限请求触发后的状态
    ///
    /// # Errors
    /// 如果 PhotoKit 框架不可用，返回错误
    pub fn request_authorization(
        &self,
        access_level: PhotoKitAccessLevel,
    ) -> Result<PhotoKitAuthorizationStatus, PhotoKitBridgeError> {
        unsafe {
            // 检查 PHPhotoLibrary 类是否可用
            let ph_photo_library_class = class!(PHPhotoLibrary);

            // 调用 PHPhotoLibrary.requestAuthorizationForAccessLevel:handler:
            // 使用空的完成处理器，因为我们将通过轮询检查状态
            let access_level_value = access_level.to_native_value();

            // 定义一个简单的空回调
            type CompletionBlock = Option<extern "C" fn(i32)>;
            let completion_block: CompletionBlock = None;

            let _: () = msg_send![
                ph_photo_library_class,
                requestAuthorizationForAccessLevel: access_level_value,
                handler: completion_block
            ];

            // 触发权限请求后，立即检查当前状态
            // 注意：实际的权限状态可能需要用户交互后才会更新
            self.check_authorization_status(access_level)
        }
    }

    /// 检查 PhotoKit 框架是否可用
    ///
    /// # Returns
    /// 如果 PhotoKit 框架可用返回 true，否则返回 false
    pub fn is_framework_available(&self) -> bool {
        // 在 macOS 10.15+ 上，PhotoKit 应该总是可用的
        // 这里我们简单返回 true，实际的可用性检查在具体方法调用时进行
        true
    }
}

impl Default for PhotoKitBridge {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_photokit_bridge_creation() {
        let bridge = PhotoKitBridge::new();
        // 在测试环境中，框架可能不可用，这是正常的
        let _ = bridge.is_framework_available();
    }

    #[test]
    fn test_photokit_bridge_default() {
        let bridge = PhotoKitBridge::default();
        let _ = bridge.is_framework_available();
    }

    #[test]
    fn test_error_display() {
        let error = PhotoKitBridgeError::FrameworkUnavailable;
        assert_eq!(error.to_string(), "PhotoKit 框架不可用");

        let error = PhotoKitBridgeError::RequestTimeout;
        assert_eq!(error.to_string(), "权限请求超时");

        let error = PhotoKitBridgeError::InvalidAccessLevel(999);
        assert_eq!(error.to_string(), "无效的权限级别: 999");
    }
}
